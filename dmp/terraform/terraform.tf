terraform {
  required_providers {
    alicloud = {
      source  = "aliyun/alicloud"
      version = "1.227.0"
    }
    ansible = {
      version = "~> 1.3.0"
      source  = "ansible/ansible"
    }
  }
#   backend "oss" {
#     endpoint = "oss-rg-china-mainland.aliyuncs.com"
#     bucket   = "tapdata-terraform-state"
#     prefix   = "cloud/state"
#     key      = "terraform.tfstate"
#     # 这里不支持使用变量提供，需要将 AK/SK 写入环境变量
#     #access_key = "" # export ALICLOUD_ACCESS_KEY=xxxx
#     #secret_key = "" # export ALICLOUD_SECRET_KEY=xxxx
#   }
}