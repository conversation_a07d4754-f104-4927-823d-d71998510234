

# Create VSwitch
resource "alicloud_vswitch" "main" {
  count        = 3
  vpc_id       = var.vpc_id
  cidr_block   = "172.16.${count.index + 1}.0/24"
  zone_id      = var.zone_ids[count.index]
  vswitch_name = "vswitch-${var.project_name}-${var.zone_ids[count.index]}"
  description  = var.description
  tags = {
    group = var.group
  }
}

output "vswitch_ids" {
  value = [for vswitch in alicloud_vswitch.main : vswitch.id]
}

output "vswitch_cidrs" {
  value = {
    for vswitch in alicloud_vswitch.main : vswitch.id => vswitch.cidr_block
  }
}