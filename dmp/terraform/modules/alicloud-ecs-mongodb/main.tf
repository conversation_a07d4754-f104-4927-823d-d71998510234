
# create ecs
resource "alicloud_instance" "mongodb" {
  count                      = 3
  instance_name              = "mongodb-${var.project_name}-${count.index + 1}"
  host_name                  = "mdb-${count.index + 1}"
  instance_type              = var.instance_types[count.index]
  security_groups            = var.security_group
  vswitch_id                 = var.vswitch_ids[count.index]
  image_id                   = var.image_id
  private_ip                 = cidrhost(var.vswitch_cidrs[var.vswitch_ids[count.index]], 5)
  instance_charge_type       = "PrePaid"
  internet_charge_type       = "PayByTraffic"
  internet_max_bandwidth_out = 10
  system_disk_category       = "cloud_essd"
  system_disk_size           = 50
  tags = {
    name  = "mongodb"
    group = var.group
  }
  include_data_disks = false

  key_name = var.key_pair_name

  data_disks {
    name      = "mongodb-disk"
    size      = 500
    category  = "cloud_essd"
    encrypted = false
  }
}

# output public ip
output "mongodb_public_ips" {
  value = [for ecs in alicloud_instance.mongodb : ecs.public_ip]
}
output "mongodb_private_ips" {
  value = [for ecs in alicloud_instance.mongodb : ecs.private_ip]
}
