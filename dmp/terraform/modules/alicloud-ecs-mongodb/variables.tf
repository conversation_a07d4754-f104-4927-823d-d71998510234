variable "security_group" {
  type = list(string)
}

variable "vswitch_ids" {
  type = list(string)
}

variable "vswitch_cidrs" {
  type = map(string)
}

variable "image_id" {
  type = string
}

variable "instance_type_2xlarge" {
  type    = string
  default = "ecs.hfg6.2xlarge" # 高主频通用型 hfg6，ecs.hfg6.2xlarge，8vCPU 32GiB
}
variable "instance_type_xlarge" {
  type    = string
  default = "ecs.hfg6.xlarge" # 高主频通用型 hfg6，ecs.hfg6.xlarge，4vCPU 16GiB
}
variable "instance_types" {
  type    = list(string)
  default = ["ecs.hfg6.2xlarge", "ecs.hfg6.2xlarge", "ecs.hfg6.xlarge"]
}

variable "key_pair_name" {
  type = string
}

variable "project_name" {
  type = string
  default = "P0"
}

variable "group" {
  type = string
  default = "G0"
}

variable "description" {
  type    = string
  default = "Managed by Terraform"
}