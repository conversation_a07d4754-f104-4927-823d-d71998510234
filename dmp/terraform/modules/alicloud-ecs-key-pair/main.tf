variable "group" {
  type = string
  default = "G0"
}

variable "project_name" {
  type = string
  default = "P0"
}

resource "alicloud_ecs_key_pair" "default" {
  key_pair_name = "ecs-user-key-pair-${project_name}"
  public_key    = file("./keys/root_id_ed25519.pub")
  tags = {
    name = "ecs-user"
    group = var.group
  }
}

output "key_pair_name" {
  value = alicloud_ecs_key_pair.default.key_pair_name
}