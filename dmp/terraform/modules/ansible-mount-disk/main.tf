
variable "ansible_private_key" {
  description = "Ansible ssh private key path"
  type        = string
}
variable "public_ips" {
  type = list(string)
  description = "List of public IPs of the instances to mount the disk"
}

resource "local_file" "default" {
  filename = "${path.module}/temp/hosts.ini"
  content = "[mount_disk]\n${join("\n", var.public_ips)}"
}

resource "null_resource" "mount-disk" {
  provisioner "local-exec" {
    command = <<EOF
      export ANSIBLE_HOST_KEY_CHECKING=False
      ansible-playbook -i ${local_file.default.filename} \
        ${path.root}/../ansible/playbooks/mount.yml \
        --extra-vars="group=mount_disk user=root private_key=${var.ansible_private_key} volume_name=vdb"

    EOF
  }
}