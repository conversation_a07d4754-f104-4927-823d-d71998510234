

# create default security group
resource "alicloud_security_group" "default" {
  name        = "security-group-${var.project_name}"
  vpc_id      = var.vpc_id
  description = var.description
  tags = {
    role  = "default"
    group = var.group
  }
}

# create allow http/https security group
resource "alicloud_security_group" "http_security_group" {
  name        = "http-security-group-${var.project_name}"
  vpc_id      = var.vpc_id
  description = var.description
  tags = {
    role  = "http"
    group = var.group
  }
}

# create allow http/https security group
resource "alicloud_security_group" "mongodb_security_group" {
  name        = "mongodb-security-group-${var.project_name}"
  vpc_id      = var.vpc_id
  description = var.description
  tags = {
    role  = "mongodb"
    group = var.group
  }
}

# add security rule，allow ICMP connect
resource "alicloud_security_group_rule" "allow_icmp" {
  type              = "ingress"
  ip_protocol       = "icmp"
  nic_type          = "intranet"
  policy            = "accept"
  port_range        = "-1/-1"
  priority          = 1
  security_group_id = alicloud_security_group.default.id
  cidr_ip           = "0.0.0.0/0"
  description       = var.description
}

# add security rule，allow SSH connect
resource "alicloud_security_group_rule" "allow_ssh" {
  type              = "ingress"
  ip_protocol       = "tcp"
  nic_type          = "intranet"
  policy            = "accept"
  port_range        = "22/22"
  priority          = 1
  security_group_id = alicloud_security_group.default.id
  cidr_ip           = "0.0.0.0/0"
  description       = var.description
}


# add security rule，allow HTTP connect
resource "alicloud_security_group_rule" "allow_http" {
  type              = "ingress"
  ip_protocol       = "tcp"
  nic_type          = "intranet"
  policy            = "accept"
  port_range        = "80/80"
  priority          = 1
  security_group_id = alicloud_security_group.http_security_group.id
  cidr_ip           = "0.0.0.0/0"
  description       = var.description
}

# add security rule，allow HTTPS connect
resource "alicloud_security_group_rule" "allow_https" {
  type              = "ingress"
  ip_protocol       = "tcp"
  nic_type          = "intranet"
  policy            = "accept"
  port_range        = "443/443"
  priority          = 1
  security_group_id = alicloud_security_group.http_security_group.id
  cidr_ip           = "0.0.0.0/0"
  description       = var.description
}

resource "alicloud_security_group_rule" "allow_mongodb" {
  type              = "ingress"
  ip_protocol       = "tcp"
  nic_type          = "intranet"
  policy            = "accept"
  port_range        = "27017/27017"
  priority          = 1
  security_group_id = alicloud_security_group.mongodb_security_group.id
  cidr_ip           = "0.0.0.0/0"
  description       = var.description
}

output "security_group_id" {
  value = alicloud_security_group.default.id
}

output "http_security_group_id" {
  value = alicloud_security_group.http_security_group.id
}

output "mongodb_security_group_id" {
  value = alicloud_security_group.mongodb_security_group.id
}