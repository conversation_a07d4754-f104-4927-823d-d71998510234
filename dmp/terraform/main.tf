
# Create VPC
resource "alicloud_vpc" "main" {
  vpc_name    = "vpc-${var.project_name}"
  cidr_block  = "**********/16"
  description = var.description
  tags = {
    group = var.group
  }
}

# Create VSwitches
module "vswitch" {
  depends_on = [alicloud_vpc.main]
  source     = "./modules/alicloud-vswitch"
  vpc_id     = alicloud_vpc.main.id
  zone_ids   = var.zones
  project_name = var.project_name
  group        = var.group
  description  = var.description
}

module "security_group" {
  depends_on  = [alicloud_vpc.main]
  source      = "./modules/alicloud-security-group"
  vpc_id      = alicloud_vpc.main.id
  project_name = var.project_name
  group        = var.group
  description  = var.description
}

module "ecs_user_key_pair" {
  source       = "./modules/alicloud-ecs-key-pair"
  group        = var.group
  project_name = var.project_name
}

module "mongodb" {
  depends_on     = [module.security_group]
  source         = "./modules/alicloud-ecs-mongodb"
  security_group = [module.security_group.security_group_id, module.security_group.mongodb_security_group_id]
  vswitch_ids    = module.vswitch.vswitch_ids
  vswitch_cidrs  = module.vswitch.vswitch_cidrs
  key_pair_name  = module.ecs_user_key_pair.key_pair_name
  image_id       = var.imageId
  project_name   = var.project_name
  group          = var.group
  description    = var.description
}

resource "time_sleep" "wait_for_ecs_connectable" {
  create_duration = "20s"
  depends_on      = [module.mongodb]
}

module "mount-disk" {
  source = "./modules/ansible-mount-disk"

  public_ips          = concat(module.mongodb.mongodb_public_ips)
  ansible_private_key = "${path.module}/keys/root_id_ed25519"

  depends_on = [time_sleep.wait_for_ecs_connectable]
}
#
# module "ansible-basic" {
#   source              = "./modules/ansible-basic"
#   all_public_ips      = concat(module.mongodb.mongodb_public_ips, module.kubernetes.kubernetes_master_public_ips, module.kubernetes.kubernetes_worker_public_ips)
#   ansible_private_key = "${path.module}/key/root_id_ed25519"
#   ansible_path        = "${path.module}/../ansible"
#
#   depends_on = [module.mount-disk]
# }
#
# module "ansible-deploy-mongo" {
#   source              = "./modules/ansible-deploy-mongo"
#   mongodb_public_ips  = module.mongodb.mongodb_public_ips
#   mongodb_private_ips = module.mongodb.mongodb_private_ips
#   ansible_path        = "${path.module}/../ansible"
#   ansible_private_key = "${path.module}/key/root_id_ed25519"
#
#   depends_on = [module.ansible-basic]
# }
#
# module "deploy-nginx" {
#   depends_on                    = [module.deploy-keepalived]
#   source                        = "./modules/ansible-deploy-nginx"
#   ansible_private_key           = "${path.module}/key/root_id_ed25519"
#   backend_ips                   = module.kubernetes.kubernetes_worker_private_ips
#   keepalived_public_ips         = slice(module.kubernetes.kubernetes_zone_cn_hongkong_b_worker_public_ips, 0, 2)
# }
