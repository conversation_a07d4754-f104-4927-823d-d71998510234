---
- name: Mount the volume
  hosts: "{{ group }}"
  user: "{{ user }}"
  become: true
  vars:
    ansible_ssh_private_key_file: "{{ private_key }}"
  tasks:
    - name: Install necessary packages
      package:
        name:
          - util-linux
          - e2fsprogs
        state: present

    - name: Check if the disk is mounted
      shell: df -h | grep '/data'
      register: checkExists
      ignore_errors: yes

    - name: Mount volume
      when: checkExists.rc != 0
      block:
        - name: Make file system
          filesystem:
            fstype: ext4
            dev: /dev/{{ volume_name }}

        - name: Make directory
          file:
            path: /data
            state: directory

        - name: Mount the disk
          mount:
            path: /data
            src: /dev/{{ volume_name }}
            fstype: ext4
            opts: discard,defaults,nofail
            passno: 2
            state: mounted

        - name: Ensure disk is mounted on boot by updating /etc/fstab
          mount:
            path: /data
            src: /dev/{{ volume_name }}
            fstype: ext4
            opts: discard,defaults,nofail
            passno: 2
            state: present
